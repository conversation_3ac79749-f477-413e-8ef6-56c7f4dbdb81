
import networkx as nx
import numpy as np
import time

def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")

# 影响力传播模拟 (IC模型)
def IC(g, seed, p, mc=1000):
    seed = set(seed)  # 转换为集合，避免重复元素
    influence = []
    for _ in range(mc):
        new_active, last_active = set(seed), set(seed)  # 使用集合来去重
        while new_active:
            new_ones = set()
            for node in new_active:
                node_neighbors = list(g.neighbors(node))  
                for neighbor in node_neighbors:
                    if np.random.uniform(0, 1) < p:
                        new_ones.add(neighbor)
            new_active = new_ones - last_active
            last_active.update(new_active)
        influence.append(len(last_active))  # 记录激活的总节点数
    return np.mean(influence)  # 返回平均影响力


def IC_vec(g, seed, p, mc=1000):
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)
    
    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]
    
    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)
    
    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)
    
    # 批量生成随机数（优化随机数生成效率）
    rand_pool = np.random.rand(mc, n*5)  # 预生成随机数池（5倍冗余）
    pool_ptr = 0
    
    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()
        
        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])
            
            if neighbors.size == 0:
                break
            
            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数
            
            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)
            
            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]
            
            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True
        
        influence[i] = active.sum()
    
    return np.mean(influence)


def main():
    network_path = "D:\\VS\\code\\networks\\AS733.txt"
    G = gen_graph(network_path)
    p = 0.05
    k = 100  # 设置要选择的顶点数量
    
    # 直接在main函数中计算度中心性并选择前k个节点
    degree_centrality = nx.degree_centrality(G)
    sorted_nodes = sorted(degree_centrality.items(), 
                         key=lambda x: x[1], 
                         reverse=True)
    # top_k_nodes = [node for node, _ in sorted_nodes[:k]]
    top_k_nodes =  [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 20, 21, 22, 24, 26, 28, 31, 41, 47, 49, 51, 52, 56, 60, 70, 76, 77, 82, 85, 86, 133, 183, 207, 369, 438, 475, 547, 550, 551, 629, 857, 871, 899, 938, 1032, 1057, 1108, 1112, 1118, 1311]

    
    # 测试两种IC模型的计算效率
    print(f"网络节点数: {G.number_of_nodes()}, 边数: {G.number_of_edges()}")
    print(f"种子节点数: {len(top_k_nodes)}")
    
    # 测试传统IC模型
    start_time = time.time()
    influence_traditional = IC(G, top_k_nodes, p, mc=1000)
    traditional_time = time.time() - start_time
    print(f"传统IC模型:")
    print(f"  - 执行时间: {traditional_time:.4f}秒")
    print(f"  - 平均影响力: {influence_traditional:.2f}")
    
    # 测试向量化IC模型
    start_time = time.time()
    influence_vectorized = IC_vec(G, top_k_nodes, p, mc=1000)
    vectorized_time = time.time() - start_time
    print(f"向量化IC模型:")
    print(f"  - 执行时间: {vectorized_time:.4f}秒")
    print(f"  - 平均影响力: {influence_vectorized:.2f}")
    
    # 计算加速比
    speedup = traditional_time / vectorized_time
    print(f"加速比: {speedup:.2f}倍")


if __name__ == "__main__":
    main()