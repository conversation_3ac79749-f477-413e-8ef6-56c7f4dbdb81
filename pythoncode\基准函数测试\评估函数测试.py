from ast import Set
from re import S
import networkx as nx
import numpy as np
import time


class GEN_GRAPH:
    """Network graph generator and neighbor cache container
    
    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """
    
    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存
        
        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）
            
        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）
        
        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）
        
        Returns:
            生成的NetworkX无向图对象
            
        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e

def EDV(S, G,p):
    S = set(S)  # 转换S为集合以去除重复项
    total = len(S)
    NS_1_S = set()  # 存储S的所有邻居，但不包括S中的节点

    # 收集S的所有邻居节点
    for s in S:
        NS_1_S.update(G.nx_G[s])

    # 从邻居集中去除S中已存在的节点
    NS_1_S.difference_update(S)

    # 计算每个邻居节点对总值的贡献
    for u in NS_1_S:
        r = sum(1 for s in S if u in G.nx_G[s])  # 检查是否存在边s-u
        total += (1 - (1 - p) ** r)

    return total

def optimized_EDV(graph, S, p):
    """
    优化后的影响力评估 (Expected Diffusion Value)
    
    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率
        
    Returns:
        float: 估计的影响力值
    """
    # 预生成邻接字典（将邻居存储为集合）
    adj_dict = graph.neighbors
    S = set(S)
    
    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    
    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(set(adj_dict[node]) & S)
        influence_sum += 1 - (1 - p) ** num_connections
    
    return len(S) + influence_sum



def optimized_EDV_with_2nd_order(graph, S, p, alpha=1.0, beta=1.0):
    """
    二阶邻居传播受一阶邻居激活约束的条件概率版EDV计算
    """
    S = set(S)
    adj_dict = {node: set(graph.neighbors(node)) for node in graph.nodes}
    
    NS_1 = set()
    for s in S:
        NS_1.update(adj_dict.get(s, set()))
    NS_1 -= S

    p_1_dict = {}
    influence_1 = 0.0
    for node in NS_1:
        num_connections = len(adj_dict[node] & S)
        p_activated = 1 - (1 - p) ** num_connections
        p_1_dict[node] = p_activated
        influence_1 += p_activated

    NS_2 = set()
    for n1 in NS_1:
        NS_2.update(adj_dict[n1])
    NS_2 -= S
    NS_2 -= NS_1

    influence_2 = 0.0
    for node in NS_2:
        first_neighbors = adj_dict[node] & NS_1
        product_term = 1.0
        for fn in first_neighbors:
            # 条件概率计算：P(u激活且传播到w失败) = 1 - P_1(u) * p
            product_term *= (1 - p * p_1_dict.get(fn, 0))
        p_activated_2 = 1 - product_term
        influence_2 += p_activated_2

    total_influence = len(S) + alpha * influence_1 + beta * influence_2
    return total_influence


def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟
    
    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数
        
    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)
    
    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]
    
    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)
    
    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)
    
    # 批量生成随机数（优化随机数生成效率）
    rand_pool = np.random.rand(mc, n*5)  # 预生成随机数池（5倍冗余）
    pool_ptr = 0
    
    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()
        
        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])
            
            if neighbors.size == 0:
                break
            
            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数
            
            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)
            
            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]
            
            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True
        
        influence[i] = active.sum()
    
    return np.mean(influence)

def LIE_two_hop(s_set, G, p):
    s_set = list(s_set)
    LIE_num = len(s_set)
    temp_sum = 0
    temp_sum_2 = 0
    neighbor_s=set()
    neighbor_ss=set()
    for i in s_set:
        neighbors = set(G.neighbors[i]) - set(s_set) #一阶邻居
        neighbors_un = set()  # 用来存储所有不在x中的二级邻居
        for neighbor in neighbors:
            neighbors_of_neighbor = set(G.neighbors[neighbor]) - set(s_set) - set(neighbors)
            neighbors_un.update(neighbors_of_neighbor)

        neighbor_ss.update(neighbors_un)
        neighbor_s.update(neighbors)
    for i in neighbor_s:
        num = sum(1 for j in s_set if i in G.nx_G[j])
        temp_sum += (1 - (1 - p) ** num)
    num2 = 0
    for i in neighbor_ss:
        for j in neighbor_s:
            if i in G.nx_G[j]:
                num2 += 1

    if neighbor_s:
        temp_sum_2 = (temp_sum * num2 * p) / len(neighbor_s)

    LIE = LIE_num + temp_sum + temp_sum_2
    return LIE

# 在向量化计算部分使用更高精度的数据类型
def vectorized_newLIE_two_hop(s_set, G, p):
    """向量化优化的二跳影响力估计函数
    
    Args:
        s_set (set): 种子节点集合
        G (nx.Graph): 网络图对象
        p (float): 传播概率
        
    Returns:
        float: 基于向量化计算的影响力估计值
    """
    s_set = set(s_set)  # 确保输入为集合类型
    # 预生成邻接字典加速查询（节点到邻居集合的映射）
    adj_dict = G.neighbors
    
    # 初始化邻居集合
    neighbor_s = set()  # 存储一阶邻居
    neighbor_ss = set()  # 存储二阶邻居
    
    # 分种子节点计算邻居
    for node in s_set:
        # 当前种子节点的一阶邻居（排除种子节点自身）
        current_neighbors = set(adj_dict[node]) - s_set
        neighbor_s.update(current_neighbors)  # 合并到全局一阶集合
        
        # 计算当前种子节点的二阶邻居
        for neighbor in current_neighbors:
            # 二阶邻居定义：邻居的邻居，排除种子节点和当前种子的一阶邻居
            second_neighbors = set(adj_dict[neighbor]) - s_set - current_neighbors
            neighbor_ss.update(second_neighbors)
    
    # 向量化计算一阶影响力部分
    # 生成每个一阶邻居与种子节点的连接数数组
    # 修改数据类型为兼容的float64
    connection_counts = np.array([len(set(adj_dict[n]) & s_set) for n in neighbor_s], dtype=np.float64)
    temp_sum = np.sum(1 - (1 - np.float64(p)) ** connection_counts)
    
    # 计算二阶影响力部分
    num2 = sum(len(set(adj_dict[ss]) & neighbor_s) for ss in neighbor_ss)  # 二阶到一阶的连接总数
    # 计算二阶影响力修正项（防止空集合导致的除零错误）
    temp_sum_2 = (temp_sum * num2 * p) / len(neighbor_s) if neighbor_s else 0
    
    # 总影响力 = 种子数 + 一阶影响力 + 二阶影响力
    return len(s_set) + temp_sum + temp_sum_2


def main():
    network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # G = gen_graph(network_path)
    G = GEN_GRAPH(network_path)
    p = 0.05
    k = 100  # 设置要选择的顶点数量
    
    # 直接在main函数中计算度中心性并选择前k个节点
    degree_centrality = nx.degree_centrality(G.nx_G)
    sorted_nodes = sorted(degree_centrality.items(), 
                         key=lambda x: x[1], 
                         reverse=True)
    top_k_nodes = [node for node, _ in sorted_nodes[:k]]
    # top_k_nodes =   [1137, 149, 6, 58, 1759, 1571, 1719, 8892, 9, 114, 19, 84, 17, 2, 560, 428, 250, 8896, 502, 148, 69, 2847, 558, 191, 3730, 60, 22, 799, 61, 1667, 48, 5, 1321, 1442, 853, 15, 405, 1020, 689, 1062, 374, 562, 182, 13, 728, 824, 1064, 304, 1583, 218]
    
    # 计算EDV并统计时间
    start_time = time.time()
    edv = EDV(top_k_nodes, G, p)
    edv_time = time.time() - start_time
    
    start_time = time.time()
    edv_optimized = optimized_EDV(G, top_k_nodes, p)
    edv_opt_time = time.time() - start_time
    
    # 计算带二阶邻居的优化EDV并统计时间
    start_time = time.time()
    edv_with_2nd = optimized_EDV_with_2nd_order(G.nx_G, top_k_nodes, p)
    edv_with_2nd_time = time.time() - start_time
    
    # 计算LIE并统计时间
    start_time = time.time()
    LIE_two_hop_ = LIE_two_hop(top_k_nodes, G, p)
    lie_time = time.time() - start_time
    
    start_time = time.time()
    vectorized_newLIE_two_hop_result = vectorized_newLIE_two_hop(top_k_nodes, G, p)
    vec_lie_time = time.time() - start_time
    
    # 计算IC模型模拟结果
    start_time = time.time()
    ic_result = IC_vec(G.nx_G, top_k_nodes, p, mc=10000)
    ic_time = time.time() - start_time

    # 打印计算结果和时间统计
    print("计算结果和时间统计:")
    print(f"EDV: {edv} (耗时: {edv_time:.10f}秒)")
    print(f"优化后的EDV: {edv_optimized} (耗时: {edv_opt_time:.10f}秒)")
    print(f"带二阶邻居的优化EDV: {edv_with_2nd} (耗时: {edv_with_2nd_time:.10f}秒)")
    print(f"LIE_two_hop_: {LIE_two_hop_} (耗时: {lie_time:.10f}秒)")
    print(f"vectorized_newLIE_two_hop_: {vectorized_newLIE_two_hop_result} (耗时: {vec_lie_time:.10f}秒)")
    print(f"IC模型模拟 (1000次): {ic_result} (耗时: {ic_time:.10f}秒)")
    
    # 计算并打印时间提升比例
    print("\n时间性能提升比例:")
    if edv_opt_time > 0:
        print(f"优化后EDV相比原始EDV提升: {(edv_time/edv_opt_time - 1)*100:.2f}%")
    else:
        print("优化后EDV执行时间过短无法计算提升比例")
    
    if edv_with_2nd_time > 0:
        print(f"带二阶邻居的EDV相比原始EDV提升: {(edv_time/edv_with_2nd_time - 1)*100:.2f}%")
    else:
        print("带二阶邻居的EDV执行时间过短无法计算提升比例")
    
    if vec_lie_time > 0:
        print(f"向量化LIE相比原始LIE提升: {(lie_time/vec_lie_time - 1)*100:.2f}%")
    else:
        print("向量化LIE执行时间过短无法计算提升比例")
    
    # 与评估函数的准确度比较
    print("\n各评估函数与IC模拟的相对误差:")
    if ic_result > 0:
        print(f"EDV相对误差: {abs((edv - ic_result)/ic_result)*100:.2f}%")
        print(f"优化EDV相对误差: {abs((edv_optimized - ic_result)/ic_result)*100:.2f}%")
        print(f"带二阶邻居的EDV相对误差: {abs((edv_with_2nd - ic_result)/ic_result)*100:.2f}%")
        print(f"LIE_two_hop相对误差: {abs((LIE_two_hop_ - ic_result)/ic_result)*100:.2f}%")
        print(f"向量化LIE相对误差: {abs((vectorized_newLIE_two_hop_result - ic_result)/ic_result)*100:.2f}%")

if __name__ == "__main__":
    main()