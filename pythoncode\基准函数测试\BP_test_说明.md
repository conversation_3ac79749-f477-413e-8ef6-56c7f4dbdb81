# BP_test.py 修改说明

## 修改内容

1. **保留功能**：
   - `BP_influence()`: 标准BP近似影响力估计
   - `IC_vec()`: IC模型模拟函数（作为基准对比）
   - `GEN_GRAPH`: 网络图生成器类

2. **删除功能**：
   - 删除了EDV评估函数
   - 删除了LIE二跳评估函数
   - 删除了Neumann级数近似函数
   - 删除了稀疏BP近似函数（optimal_BP_influence）
   - 删除了时间统计功能

3. **新增功能**：
   - 添加了误差计算：BP近似相对于IC模型的误差
   - 添加了误差类型判断（高估/低估）

4. **参数设置**：
   - max_hop设定为：[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]（更细粒度测试）
   - 传播概率p：[0.01, 0.05, 0.1]
   - 种子节点数k：50（基于度中心性选择）
   - IC模拟次数：10000次（提高精度）

5. **输出结果**：
   - 结果保存在`BP_result`文件夹中
   - 每个网络生成一个独立的Excel文件
   - 文件命名格式：`BP_{网络名称}_结果.xlsx`
   - **新增**：每个网络文件中误差最小的max_hop行会被标记为黄色背景
   - **新增**：生成汇总文件`BP_最优参数汇总.xlsx`，包含所有网络的最优参数

## 结果文件结构

### 单个网络结果文件
每个Excel文件包含以下列：
- 网络：网络名称
- 传播概率：p值（0.01, 0.05, 0.1）
- max_hop：最大跳数（1-10）
- BP近似：BP算法计算结果
- IC模型：IC模型模拟结果（基准值）
- 相对误差(%)：BP相对于IC的误差百分比
- 误差类型：高估或低估

**特殊标记**：每个传播概率下误差最小的行会被标记为黄色背景

### 汇总文件
`BP_最优参数汇总.xlsx`包含：
- 所有网络在每个传播概率下的最优max_hop参数
- 对应的BP近似值、IC基准值和误差信息
- 所有行都标记为黄色背景（表示最优参数）

## 测试网络

程序测试了以下6个网络：
1. netscience.txt
2. email.txt
3. blog.txt
4. pgp.txt
5. CA-HepTh.txt
6. NetHEHT.txt

## 运行方式

```bash
python BP_test.py
```

程序会自动：
1. 创建`BP_result`文件夹
2. 对每个网络和每个传播概率先计算IC模型基准
3. 对每个max_hop值（1-10）计算BP影响力
4. 计算BP相对于IC的误差并判断误差类型
5. 为每个传播概率的最小误差行添加黄色高亮
6. 保存结果到对应的Excel文件中
7. 生成包含所有最优参数的汇总文件
8. 在控制台显示实时进度、结果和误差分析

## 主要观察结果

从测试结果可以看出：
- **max_hop=1-2时**：BP算法通常会低估影响力，误差较大
- **max_hop=3-5时**：BP算法精度显著提高，误差通常在1%以内
- **max_hop=6-10时**：精度进一步提高，但有时会出现轻微高估
- **传播概率越高**：需要更大的max_hop值才能达到最佳精度
- **不同网络结构**：对最优max_hop参数的影响显著

## 最优参数总结

根据汇总文件的结果：
- **低传播概率(p=0.01)**：最优max_hop通常为2-5
- **中传播概率(p=0.05)**：最优max_hop通常为3-5
- **高传播概率(p=0.1)**：最优max_hop通常为3-6
- **总体趋势**：传播概率越高，所需的最优max_hop越大
