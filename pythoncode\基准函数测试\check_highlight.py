import pandas as pd
from openpyxl import load_workbook

def check_highlights():
    """检查高亮是否正确应用"""
    
    # 检查netscience网络的结果
    df = pd.read_excel('BP_result/BP_netscience_结果.xlsx')
    print("netscience网络结果:")
    print(df)
    
    print("\n按传播概率分组的最小误差:")
    for p in [0.01, 0.05, 0.1]:
        p_data = df[df['传播概率'] == p].copy()
        p_data['abs_error'] = p_data['相对误差(%)'].astype(float).abs()
        min_idx = p_data['abs_error'].idxmin()
        print(f"p={p}: max_hop={p_data.loc[min_idx, 'max_hop']}, 误差={p_data.loc[min_idx, '相对误差(%)']}")
    
    # 检查汇总文件
    print("\n汇总文件内容:")
    summary_df = pd.read_excel('BP_result/BP_最优参数汇总.xlsx')
    print(summary_df)
    
    # 检查Excel文件的高亮
    print("\n检查Excel文件高亮...")
    wb = load_workbook('BP_result/BP_netscience_结果.xlsx')
    ws = wb.active
    
    print("检查第2行(max_hop=2, p=0.01)是否有黄色背景:")
    cell = ws.cell(row=2, column=1)  # 第2行第1列
    print(f"填充颜色: {cell.fill.start_color.index}")
    
    print("检查第13行(max_hop=3, p=0.05)是否有黄色背景:")
    cell = ws.cell(row=13, column=1)  # 第13行第1列
    print(f"填充颜色: {cell.fill.start_color.index}")

if __name__ == "__main__":
    check_highlights()
